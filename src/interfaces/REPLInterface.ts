import readline from 'readline';
import http from 'http';
import os from 'os';
import blessed from 'blessed';
import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { logger } from '../utils/Logger';


export class REPLInterface implements MessageInterface {
  private rl: readline.Interface | null = null;
  private screen: blessed.Widgets.Screen | null = null;
  private chatBox: blessed.Widgets.ScrollableBoxElement | null = null;
  private inputBox: blessed.Widgets.TextboxElement | null = null;
  private serverUrl: string;
  private serverPort: number;
  private currentConversationId: number | null = null;
  private lastMessageId: number = 0;
  private pollingInterval: NodeJS.Timeout | null = null;
  private sessionId: string | null = null;
  private userIdentifier: string;
  private useBlessedUI: boolean = true; // RE-ENABLED - console redirect was the issue

  constructor(serverUrl: string = 'localhost', serverPort: number = 3000, sessionId?: string) {
    this.serverUrl = serverUrl;
    this.serverPort = serverPort;
    this.sessionId = sessionId || null;
    this.userIdentifier = `${os.hostname()}_${os.userInfo().username}`;

    // CRITICAL: Restore original console methods before initializing blessed
    // The logger may have redirected console output to files, which interferes with blessed
    logger.restoreConsole();

    if (this.useBlessedUI) {
      // Initialize blessed screen and UI components
      // Give blessed full control over stdin/stdout
      this.screen = blessed.screen({
        smartCSR: true,
        title: 'ForaChat REPL',
        input: process.stdin,
        output: process.stdout,
        terminal: 'xterm-256color',
        fullUnicode: true
      });

      // Create chat display area (scrollable)
      this.chatBox = blessed.box({
        parent: this.screen,
        top: 0,
        left: 0,
        width: '100%',
        height: '100%-3',
        content: '',
        tags: true,
        scrollable: true,
        alwaysScroll: true,
        scrollbar: {
          ch: ' ',
          track: {
            bg: 'cyan'
          },
          style: {
            inverse: true
          }
        },
        border: {
          type: 'line'
        },
        style: {
          fg: 'white',
          bg: 'black',
          border: {
            fg: '#f0f0f0'
          }
        }
      });

      // Create input area at bottom
      this.inputBox = blessed.textbox({
        parent: this.screen,
        bottom: 0,
        left: 0,
        width: '100%',
        height: 3,
        inputOnFocus: true,
        border: {
          type: 'line'
        },
        style: {
          fg: 'white',
          bg: 'black',
          border: {
            fg: '#f0f0f0'
          }
        },
        label: ' Input (Press Enter to send, Ctrl+C to exit) '
      });

      // DON'T create readline interface when using blessed - it conflicts with stdin
      // Blessed will handle all input directly
      this.setupEventHandlers();
    } else {
      // Use simple readline interface for debugging
      this.rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
        prompt: '> '
      });
      this.setupSimpleEventHandlers();
    }
  }

  private setupEventHandlers(): void {
    if (!this.inputBox || !this.screen) return;

    // Handle input submission
    this.inputBox.on('submit', async (value: string) => {
      const input = value.trim();

      if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
        this.cleanup();
        return;
      }

      if (input.length === 0) {
        this.inputBox?.clearValue();
        this.screen?.render();
        return;
      }

      // Clear input and add user message to chat
      this.inputBox?.clearValue();
      this.addMessageToChat('user', input);

      try {
        await this.sendMessage(input);
      } catch (error) {
        this.addMessageToChat('system', `Error: ${(error as Error).message}`, 'red');
      }

      this.screen?.render();
    });

    // Handle Ctrl+C to exit
    this.screen.key(['C-c'], () => {
      this.cleanup();
    });

    // Handle screen resize
    this.screen.on('resize', () => {
      this.screen?.render();
    });
  }

  private setupSimpleEventHandlers(): void {
    if (!this.rl) {
      console.error('🔧 ERROR: Readline interface not initialized');
      return;
    }

    // Handle Ctrl+C to exit
    process.on('SIGINT', () => {
      console.log('\n👋 Thanks for using ForaChat! Have a great day!');
      this.cleanup();
    });

    // Set up readline for simple input
    this.rl.on('line', async (input: string) => {
      const trimmedInput = input.trim();

      if (trimmedInput.toLowerCase() === 'exit' || trimmedInput.toLowerCase() === 'quit') {
        this.cleanup();
        return;
      }

      if (trimmedInput.length === 0) {
        this.rl?.prompt();
        return;
      }

      console.log(`[${new Date().toLocaleTimeString()}] user: ${trimmedInput}`);

      try {
        await this.sendMessage(trimmedInput);
      } catch (error) {
        console.log(`[${new Date().toLocaleTimeString()}] system: Error: ${(error as Error).message}`);
      }

      this.rl?.prompt();
    });
  }

  private addMessageToChat(character: string, message: string, color: string = 'white'): void {
    const timestamp = new Date().toLocaleTimeString();

    if (this.useBlessedUI && this.chatBox && this.screen) {
      const formattedMessage = `{${color}-fg}[${timestamp}] ${character}: ${message}{/}`;

      // Add message to chat box
      const currentContent = this.chatBox.getContent();
      const newContent = currentContent + (currentContent ? '\n' : '') + formattedMessage;
      this.chatBox.setContent(newContent);

      // Auto-scroll to bottom
      this.chatBox.setScrollPerc(100);
      this.screen.render();
    } else {
      // Simple console output for debugging
      console.log(`[${timestamp}] ${character}: ${message}`);
    }
  }

  private cleanup(): void {
    // Clean up polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    // Destroy blessed screen if it exists
    if (this.screen) {
      this.screen.destroy();
    }

    // Close readline if it exists
    if (this.rl) {
      this.rl.close();
    }

    console.log('\n👋 Thanks for using ForaChat! Have a great day!');
    process.exit(0);
  }

  async start(): Promise<void> {
    // Add welcome messages to chat
    this.addMessageToChat('system', '🚀 ForaChat REPL started!', 'green');
    this.addMessageToChat('system', 'Type your workplace questions and get advice from Fora, Jan, and Lou.', 'cyan');

    // Initialize or restore session
    await this.initializeSession();

    this.addMessageToChat('system', 'Type "exit" or "quit" to end the session.', 'yellow');

    if (this.useBlessedUI) {
      this.addMessageToChat('system', 'Press Enter to send messages, Ctrl+C to exit.', 'yellow');
      // Focus input box and render screen
      this.inputBox?.focus();
      this.screen?.render();
    } else {
      this.addMessageToChat('system', 'Type your message and press Enter. Ctrl+C to exit.', 'yellow');
      this.rl?.prompt();
    }
  }

  private async initializeSession(): Promise<void> {
    try {
      if (this.sessionId) {
        // Try to restore existing session
        const sessionInfo = await this.getSessionInfo(this.sessionId);
        if (sessionInfo) {
          this.currentConversationId = sessionInfo.conversationId;
          this.addMessageToChat('system', `📋 Restored session: ${this.sessionId}`, 'green');
          if (sessionInfo.conversationId) {
            this.addMessageToChat('system', `💬 Continuing conversation: ${sessionInfo.conversationId}`, 'green');
            // Optionally show recent messages
            await this.showRecentMessages(sessionInfo.conversationId);
          }
        } else {
          this.addMessageToChat('system', `⚠️  Session ${this.sessionId} not found, creating new session`, 'yellow');
          this.sessionId = null;
        }
      }

      if (!this.sessionId) {
        // Create new session
        this.sessionId = await this.createSession();
        this.addMessageToChat('system', `🆕 Created new session: ${this.sessionId}`, 'green');
      }
    } catch (error) {
      this.addMessageToChat('system', `Error initializing session: ${(error as Error).message}`, 'red');
      this.addMessageToChat('system', 'Continuing without session management...', 'yellow');
    }
  }

  private async createSession(): Promise<string> {
    const sessionRequest: SessionCreateRequest = {
      userIdentifier: `repl_${this.userIdentifier}`,
      channel: 'repl',
      metadata: {
        hostname: os.hostname(),
        username: os.userInfo().username,
        platform: os.platform()
      }
    };

    const postData = JSON.stringify(sessionRequest);
    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: '/api/session',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve(response.sessionId || response.id);
          } catch (error) {
            reject(error);
          }
        });
      });
      req.on('error', reject);
      req.write(postData);
      req.end();
    });
  }

  private async getSessionInfo(sessionId: string): Promise<any> {
    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: `/api/session/${sessionId}`,
      method: 'GET'
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          try {
            if (res.statusCode === 404) {
              resolve(null);
            } else {
              const response = JSON.parse(data);
              resolve(response);
            }
          } catch (error) {
            reject(error);
          }
        });
      });
      req.on('error', reject);
      req.end();
    });
  }

  private async showRecentMessages(conversationId: number): Promise<void> {
    try {
      const options = {
        hostname: this.serverUrl,
        port: this.serverPort,
        path: `/conversation/${conversationId}`,
        method: 'GET'
      };

      const messages = await new Promise<any>((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });
          res.on('end', () => {
            try {
              const response = JSON.parse(data);
              resolve(response.messages || []);
            } catch (error) {
              reject(error);
            }
          });
        });
        req.on('error', reject);
        req.end();
      });

      if (messages.length > 0) {
        this.addMessageToChat('system', '📜 Recent conversation history:', 'cyan');
        const recentMessages = messages.slice(-6); // Show last 6 messages
        recentMessages.forEach((msg: any) => {
          const color = msg.character === 'user' ? 'white' : 'cyan';
          this.addMessageToChat(msg.character, msg.text, color);
        });
      }
    } catch (error) {
      this.addMessageToChat('system', 'Could not load conversation history', 'red');
    }
  }

  async sendMessage(message: string): Promise<void> {
    const postData = JSON.stringify({ text: message });

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: '/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', async () => {
          try {
            const response = JSON.parse(data);
            await this.displayResponse(response);
            resolve();
          } catch (error) {
            console.error('Failed to parse response:', error);
            console.error('Raw response:', data);
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        console.error(`Connection error: ${e.message}`);
        console.error('Make sure the ForaChat server is running on port 3000');
        reject(e);
      });

      req.write(postData);
      req.end();
    });
  }

  async receiveMessage(): Promise<string> {
    return new Promise((resolve) => {
      if (this.rl) {
        this.rl.question('> ', (answer) => {
          resolve(answer.trim());
        });
      } else {
        resolve('');
      }
    });
  }

  formatResponse(response: ChatResponse): string {
    let formatted = `\n--- ${response.theme || 'Chat Response'} ---\n`;

    response.reply.forEach((message) => {
      formatted += `${message.character}: ${message.text}\n`;
    });

    if (response.skills && response.skills.length > 0) {
      formatted += `\nSkills: ${response.skills.join(', ')}\n`;
    }

    return formatted;
  }

  private async displayResponse(response: any): Promise<void> {
    // Handle error responses
    if (response.error) {
      this.addMessageToChat('system', `❌ Error: ${response.error}`, 'red');
      if (response.details) {
        this.addMessageToChat('system', `Details: ${response.details}`, 'red');
      }
      return;
    }

    // Handle successful responses with reply array
    if (response.reply && Array.isArray(response.reply)) {
      // Display theme header
      this.addMessageToChat('system', `--- ${response.theme || 'Chat Response'} ---`, 'yellow');

      // Display messages with staggered delays and timestamps
      await this.displayStaggeredMessages(response.reply);

      // Display skills if present
      if (response.skills && response.skills.length > 0) {
        this.addMessageToChat('system', `Skills: ${response.skills.join(', ')}`, 'magenta');
      }

      // Store conversation ID and start polling for delayed thoughts
      if (response.conversationId) {
        this.currentConversationId = response.conversationId;
        // Set initial lastMessageId to avoid re-showing initial messages
        this.setInitialLastMessageId();
        this.startPollingForDelayedThoughts();
      }
    } else {
      this.addMessageToChat('system', `Unexpected response format: ${JSON.stringify(response, null, 2)}`, 'red');
    }
  }

  private async displayStaggeredMessages(messages: any[]): Promise<void> {
    let cumulativeDelay = 0;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const delay = message.delay || 0;

      console.log(`🔧 DEBUG: Message ${i + 1}: character=${message.character}, delay=${delay}ms`);

      // Add the delay for this message to cumulative delay
      cumulativeDelay += delay;

      // Wait for the cumulative delay
      if (cumulativeDelay > 0) {
        console.log(`🔧 DEBUG: Waiting ${cumulativeDelay}ms before showing message`);
        await new Promise(resolve => setTimeout(resolve, cumulativeDelay));
      }

      // Display the message with character-specific colors
      const color = this.getCharacterColor(message.character);
      this.addMessageToChat(message.character, message.text, color);

      // Reset cumulative delay for next message (each message's delay is independent)
      cumulativeDelay = 0;
    }

    console.log('🔧 DEBUG: All staggered messages displayed');
  }

  private getCharacterColor(character: string): string {
    switch (character.toLowerCase()) {
      case 'fora': return 'green';
      case 'jan': return 'blue';
      case 'lou': return 'magenta';
      case 'user': return 'white';
      case 'system': return 'yellow';
      default: return 'cyan';
    }
  }

  private async setInitialLastMessageId(): Promise<void> {
    if (!this.currentConversationId) return;

    try {
      const options = {
        hostname: this.serverUrl,
        port: this.serverPort,
        path: `/conversation/${this.currentConversationId}`,
        method: 'GET'
      };

      return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const response = JSON.parse(data);
              if (response.messages && Array.isArray(response.messages)) {
                // Set lastMessageId to the highest current message ID
                const messageIds = response.messages.map((msg: any) => msg.id);
                if (messageIds.length > 0) {
                  this.lastMessageId = Math.max(...messageIds);
                }
              }
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        });

        req.on('error', (e) => {
          reject(e);
        });

        req.end();
      });
    } catch (error) {
      // Silently handle errors
    }
  }

  private startPollingForDelayedThoughts(): void {
    // Clear any existing polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    this.addMessageToChat('system', '⏳ Listening for character follow-up thoughts...', 'yellow');

    // Poll every 10 seconds for delayed thoughts
    this.pollingInterval = setInterval(async () => {
      try {
        await this.checkForDelayedThoughts();
      } catch (error) {
        // Silently handle errors to avoid cluttering the REPL
      }
    }, 10000);

    // Stop polling after 10 minutes
    setTimeout(() => {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
        this.addMessageToChat('system', '⏰ Stopped listening for character thoughts.', 'yellow');
      }
    }, 600000); // 10 minutes
  }

  private async checkForDelayedThoughts(): Promise<void> {
    if (!this.currentConversationId) return;

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: `/conversation/${this.currentConversationId}`,
      method: 'GET'
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.messages && Array.isArray(response.messages)) {
              // Filter for new messages since last check
              const newMessages = response.messages.filter((msg: any) =>
                msg.id > this.lastMessageId && msg.character !== 'user'
              );

              if (newMessages.length > 0) {
                this.addMessageToChat('system', '💭 Character follow-up thoughts:', 'cyan');
                newMessages.forEach((msg: any) => {
                  const color = this.getCharacterColor(msg.character);
                  this.addMessageToChat(msg.character, msg.text, color);
                });

                // Update last message ID
                this.lastMessageId = Math.max(...newMessages.map((msg: any) => msg.id));
              }
            }
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      req.end();
    });
  }

  // Utility methods for enhanced REPL experience
  setPrompt(prompt: string): void {
    this.rl?.setPrompt(prompt);
  }

  displayHelp(): void {
    this.addMessageToChat('system', '📚 ForaChat Help:', 'cyan');
    this.addMessageToChat('system', '- Ask questions about workplace skills like communication, leadership, teamwork', 'white');
    this.addMessageToChat('system', 'Examples:', 'white');
    this.addMessageToChat('system', '  • "How can I give better feedback to my team?"', 'white');
    this.addMessageToChat('system', '  • "I\'m having conflict with a coworker"', 'white');
    this.addMessageToChat('system', '  • "How do I run more effective meetings?"', 'white');
    this.addMessageToChat('system', '- Type "exit" or "quit" to end the session', 'white');
    this.addMessageToChat('system', '- The team (Fora, Jan, Lou) will provide practical advice!', 'white');
  }

  close(): void {
    this.rl?.close();
  }
}
